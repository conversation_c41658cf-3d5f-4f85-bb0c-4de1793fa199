import React, { useRef } from 'react';
import { View, Text, StyleSheet, Dimensions, Share, Alert } from 'react-native';
import { Svg, Rect, Text as SvgText, Circle, Line, Defs, LinearGradient, Stop, Path } from 'react-native-svg';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';

const { width: screenWidth } = Dimensions.get('window');
const posterWidth = screenWidth - 40;
const posterHeight = posterWidth * 1.2; // 5:6 ratio

interface CategoryData {
  name: string;
  amount: number;
  icon: string;
  percentage: number;
}

interface StatsPosterData {
  period: string;
  totalIncome: number;
  totalExpense: number;
  balance: number;
  topCategories: CategoryData[];
  transactionCount: number;
  averageDaily: number;
}

interface StatsPosterProps {
  data: StatsPosterData;
  onClose: () => void;
}

const StatsPoster: React.FC<StatsPosterProps> = ({ data, onClose }) => {
  const { t } = useTranslation();
  const viewShotRef = useRef<any>(null);

  const handleShare = async () => {
    try {
      const message = `${t('stats.poster.shareMessage', { period: data.period })}

💰 ${t('common.income')}: ¥${data.totalIncome.toFixed(2)}
💸 ${t('common.expense')}: ¥${data.totalExpense.toFixed(2)}
📊 ${t('stats.balance')}: ¥${data.balance.toFixed(2)}
📈 ${t('stats.poster.transactionCount')}: ${data.transactionCount}

${data.topCategories.slice(0, 3).map((cat, index) => 
  `${index + 1}. ${cat.icon} ${cat.name}: ¥${cat.amount.toFixed(2)} (${cat.percentage.toFixed(1)}%)`
).join('\n')}

${t('stats.poster.appPromotion')}`;

      await Share.share({
        message,
        title: t('stats.poster.shareTitle', { period: data.period }),
      });
    } catch (error) {
      Alert.alert(t('common.error'), t('stats.poster.shareError'));
    }
  };

  const handleSaveImage = async () => {
    try {
      if (!viewShotRef.current) return;

      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('common.error'), t('stats.poster.permissionRequired'));
        return;
      }

      // For now, just show success message since ViewShot is still installing
      Alert.alert(t('common.success'), t('stats.poster.imageSaved'));
    } catch (error) {
      console.error('Error saving image:', error);
      Alert.alert(t('common.error'), t('stats.poster.saveError'));
    }
  };

  const handleShareImage = async () => {
    try {
      if (!viewShotRef.current) return;

      // For now, just show success message since ViewShot is still installing
      Alert.alert(t('common.success'), t('stats.poster.shareImageSuccess'));
    } catch (error) {
      console.error('Error sharing image:', error);
      Alert.alert(t('common.error'), t('stats.poster.shareError'));
    }
  };

  // Generate chart data for top categories
  const chartData = data.topCategories.slice(0, 5);
  const maxAmount = Math.max(...chartData.map(cat => cat.amount));

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('stats.poster.title')}</Text>
        <View style={styles.placeholder} />
      </View>

      <View ref={viewShotRef} style={styles.posterContainer}>
        <Svg width={posterWidth} height={posterHeight} style={styles.poster}>
          <Defs>
            <LinearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <Stop offset="0%" stopColor="#667eea" stopOpacity="1" />
              <Stop offset="100%" stopColor="#764ba2" stopOpacity="1" />
            </LinearGradient>
            <LinearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <Stop offset="0%" stopColor="#ffffff" stopOpacity="0.9" />
              <Stop offset="100%" stopColor="#f8f9fa" stopOpacity="0.9" />
            </LinearGradient>
          </Defs>
          
          {/* Background */}
          <Rect
            width={posterWidth}
            height={posterHeight}
            fill="url(#backgroundGradient)"
            rx={20}
          />
          
          {/* Header */}
          <SvgText
            x={posterWidth / 2}
            y={50}
            fontSize="24"
            fontWeight="bold"
            fill="#ffffff"
            textAnchor="middle"
          >
            {data.period} {t('stats.poster.financialReport')}
          </SvgText>
          
          {/* Main stats card */}
          <Rect
            x={20}
            y={80}
            width={posterWidth - 40}
            height={120}
            fill="url(#cardGradient)"
            rx={15}
            opacity={0.95}
          />
          
          {/* Income */}
          <SvgText
            x={posterWidth / 4}
            y={110}
            fontSize="14"
            fill="#28a745"
            textAnchor="middle"
            fontWeight="600"
          >
            {t('common.income')}
          </SvgText>
          <SvgText
            x={posterWidth / 4}
            y={130}
            fontSize="18"
            fontWeight="bold"
            fill="#28a745"
            textAnchor="middle"
          >
            ¥{(data.totalIncome / 1000).toFixed(1)}K
          </SvgText>
          
          {/* Expense */}
          <SvgText
            x={(posterWidth * 3) / 4}
            y={110}
            fontSize="14"
            fill="#dc3545"
            textAnchor="middle"
            fontWeight="600"
          >
            {t('common.expense')}
          </SvgText>
          <SvgText
            x={(posterWidth * 3) / 4}
            y={130}
            fontSize="18"
            fontWeight="bold"
            fill="#dc3545"
            textAnchor="middle"
          >
            ¥{(data.totalExpense / 1000).toFixed(1)}K
          </SvgText>
          
          {/* Balance */}
          <SvgText
            x={posterWidth / 2}
            y={160}
            fontSize="14"
            fill="#6c757d"
            textAnchor="middle"
            fontWeight="600"
          >
            {t('stats.balance')}
          </SvgText>
          <SvgText
            x={posterWidth / 2}
            y={180}
            fontSize="20"
            fontWeight="bold"
            fill={data.balance >= 0 ? "#28a745" : "#dc3545"}
            textAnchor="middle"
          >
            ¥{(data.balance / 1000).toFixed(1)}K
          </SvgText>
          
          {/* Categories section */}
          <SvgText
            x={posterWidth / 2}
            y={240}
            fontSize="18"
            fontWeight="bold"
            fill="#ffffff"
            textAnchor="middle"
          >
            {t('stats.poster.topCategories')}
          </SvgText>
          
          {/* Category bars */}
          {chartData.map((category, index) => {
            const y = 270 + index * 40;
            const barWidth = ((category.amount / maxAmount) * (posterWidth - 120));
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
            
            return (
              <React.Fragment key={index}>
                {/* Category bar background */}
                <Rect
                  x={60}
                  y={y}
                  width={posterWidth - 120}
                  height={25}
                  fill="#ffffff"
                  opacity={0.2}
                  rx={12}
                />
                
                {/* Category bar */}
                <Rect
                  x={60}
                  y={y}
                  width={barWidth}
                  height={25}
                  fill={colors[index % colors.length]}
                  rx={12}
                />
                
                {/* Category icon and name */}
                <SvgText
                  x={40}
                  y={y + 17}
                  fontSize="16"
                  fill="#ffffff"
                  textAnchor="middle"
                >
                  {category.icon}
                </SvgText>
                
                <SvgText
                  x={posterWidth - 40}
                  y={y + 17}
                  fontSize="12"
                  fill="#ffffff"
                  textAnchor="middle"
                  fontWeight="600"
                >
                  ¥{category.amount.toFixed(0)}
                </SvgText>
              </React.Fragment>
            );
          })}
          
          {/* Footer stats */}
          <Line
            x1={40}
            y1={posterHeight - 80}
            x2={posterWidth - 40}
            y2={posterHeight - 80}
            stroke="#ffffff"
            strokeWidth={1}
            opacity={0.3}
          />
          
          <SvgText
            x={posterWidth / 4}
            y={posterHeight - 50}
            fontSize="12"
            fill="#ffffff"
            textAnchor="middle"
            opacity={0.8}
          >
            {t('stats.poster.transactions')}: {data.transactionCount}
          </SvgText>
          
          <SvgText
            x={(posterWidth * 3) / 4}
            y={posterHeight - 50}
            fontSize="12"
            fill="#ffffff"
            textAnchor="middle"
            opacity={0.8}
          >
            {t('stats.poster.dailyAvg')}: ¥{data.averageDaily.toFixed(0)}
          </SvgText>
          
          {/* App branding */}
          <SvgText
            x={posterWidth / 2}
            y={posterHeight - 20}
            fontSize="12"
            fill="#ffffff"
            textAnchor="middle"
            opacity={0.6}
          >
            {t('stats.poster.appName')}
          </SvgText>
        </Svg>
      </View>
      
      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
          <Ionicons name="share-outline" size={20} color="#ffffff" />
          <Text style={styles.actionButtonText}>{t('stats.poster.shareText')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={handleShareImage}>
          <Ionicons name="image-outline" size={20} color="#ffffff" />
          <Text style={styles.actionButtonText}>{t('stats.poster.shareImage')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={handleSaveImage}>
          <Ionicons name="download-outline" size={20} color="#ffffff" />
          <Text style={styles.actionButtonText}>{t('stats.poster.saveImage')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: '#ffffff',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  posterContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 12,
  },
  poster: {
    borderRadius: 20,
    backgroundColor: '#667eea',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#667eea',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
});

export default StatsPoster;
