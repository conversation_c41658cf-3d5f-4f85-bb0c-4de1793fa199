import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert, Modal, TextInput } from 'react-native';
import i18n from '../i18n';
import { getTransactions, updateTransaction } from '../constants/Storage';
import { useTransactionContext } from '../context/TransactionContext';
import { useSettings } from '../context/SettingsContext';
import { useTheme } from '../context/ThemeContext';
import EmptyState from '../components/EmptyState';

interface Transaction {
  id: number;
  type: 'income' | 'expense';
  amount: number;
  category: string;
  categoryIcon: string;
  note: string;
  date: string;
  member_id: number;
  refunded: boolean;
  refund_amount?: number;
  reimbursement_status?: 'none' | 'pending' | 'completed';
  tags?: number[];
  exclude_from_budget: boolean;
  shopping_platform: string;
}

const ReimbursementScreen = () => {
  const [pendingTransactions, setPendingTransactions] = useState<Transaction[]>([]);
  const [completedTransactions, setCompletedTransactions] = useState<Transaction[]>([]);
  const [activeTab, setActiveTab] = useState<'pending' | 'completed'>('pending');
  const [showReimbursementModal, setShowReimbursementModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [reimbursementAmount, setReimbursementAmount] = useState('');
  const { refreshTrigger, triggerRefresh } = useTransactionContext();
  const { currency } = useSettings();
  const { theme } = useTheme();

  // 加载待报销和已报销的交易
  const loadReimbursementTransactions = async () => {
    try {
      // 获取所有支出交易
      const { transactions: allTransactions } = await getTransactions({
        page: 1,
        pageSize: 1000, // 获取所有记录
        filter: 'expense'
      });

      // 将分组的交易转换为平面数组
      const flatTransactions: Transaction[] = [];
      Object.values(allTransactions).forEach(dayTransactions => {
        flatTransactions.push(...dayTransactions);
      });

      // 筛选待报销和已报销的交易
      const pending = flatTransactions.filter(t => t.reimbursement_status === 'pending');
      const completed = flatTransactions.filter(t => t.reimbursement_status === 'completed');

      setPendingTransactions(pending);
      setCompletedTransactions(completed);
    } catch (error) {
      console.error('Failed to load reimbursement transactions:', error);
    }
  };

  useEffect(() => {
    loadReimbursementTransactions();
  }, [refreshTrigger]);

  // 标记为已报销 - 全额报销
  const handleMarkAsReimbursed = async (transaction: Transaction) => {
    try {
      // 全额报销时，设置退款金额等于原始金额，这样在统计中会被减去
      const originalAmount = Math.abs(transaction.amount);
      await updateTransaction(transaction.id, {
        reimbursement_status: 'completed',
        refunded: true,
        refund_amount: originalAmount
      });
      loadReimbursementTransactions();
      triggerRefresh();
    } catch (error) {
      console.error('Failed to mark as reimbursed:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('reimbursement.reimbursementFailed'));
    }
  };

  // 部分报销 - 支持部分报销也算作报销完成
  const handlePartialReimbursement = async () => {
    if (!selectedTransaction || !reimbursementAmount) {
      Alert.alert(i18n.t('common.error'), i18n.t('common.pleaseEnterAmount'));
      return;
    }

    const reimbursementAmountNum = parseFloat(reimbursementAmount);
    const originalAmount = Math.abs(selectedTransaction.amount);

    if (reimbursementAmountNum <= 0 || reimbursementAmountNum > originalAmount) {
      Alert.alert(
        i18n.t('common.error'),
        i18n.t('reimbursement.invalidReimbursementAmount', { max: originalAmount.toFixed(2) })
      );
      return;
    }

    try {
      // 任何金额的报销都标记为已完成，不再区分部分和全额
      await updateTransaction(selectedTransaction.id, {
        reimbursement_status: 'completed',
        refunded: true,
        refund_amount: reimbursementAmountNum
      });

      setShowReimbursementModal(false);
      setSelectedTransaction(null);
      setReimbursementAmount('');
      loadReimbursementTransactions();
      triggerRefresh();
    } catch (error) {
      console.error('Failed to process reimbursement:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('reimbursement.reimbursementFailed'));
    }
  };

  // 渲染交易项 - 优化UI设计
  const renderTransactionItem = (transaction: Transaction) => {
    return (
      <View key={transaction.id} style={[styles.transactionItem, {
        backgroundColor: theme.surface,
        borderColor: theme.border,
        shadowColor: theme.shadow
      }]}>
        <View style={styles.transactionLeft}>
          <View style={[styles.categoryIcon, { backgroundColor: theme.expenseBackground }]}>
            <Text style={styles.iconText}>{transaction.categoryIcon}</Text>
          </View>
          <View style={styles.transactionInfo}>
            <Text style={[styles.transactionCategory, { color: theme.text }]}>
              {transaction.category}
            </Text>
            {transaction.note && (
              <Text style={[styles.transactionNote, { color: theme.textSecondary }]} numberOfLines={2}>
                {transaction.note}
              </Text>
            )}
            <Text style={[styles.transactionDate, { color: theme.textTertiary }]}>
              {new Date(transaction.date).toLocaleDateString()}
            </Text>
          </View>
        </View>
        <View style={styles.transactionRight}>
          <Text style={[styles.transactionAmount, { color: theme.expense }]}>
            -{currency}{Math.abs(transaction.amount).toFixed(2)}
          </Text>
          {/* 显示已报销金额 */}
          {activeTab === 'completed' && transaction.refund_amount && transaction.refund_amount > 0 && (
            <View style={styles.reimbursedAmountContainer}>
              <Text style={[styles.reimbursedAmount, { color: '#4CAF50' }]}>
                +{currency}{transaction.refund_amount.toFixed(2)}
              </Text>
              <Text style={[styles.reimbursedLabel, { color: '#4CAF50' }]}>
                {i18n.t('reimbursement.reimbursed')}
              </Text>
            </View>
          )}
          {activeTab === 'pending' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.actionButton, styles.customAmountButton, { backgroundColor: theme.primary + '15', borderColor: theme.primary }]}
                onPress={() => {
                  setSelectedTransaction(transaction);
                  setReimbursementAmount('');
                  setShowReimbursementModal(true);
                }}
              >
                <Text style={[styles.actionButtonText, { color: theme.primary }]}>
                  {i18n.t('reimbursement.customAmount')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, styles.fullAmountButton, { backgroundColor: theme.primary }]}
                onPress={() => handleMarkAsReimbursed(transaction)}
              >
                <Text style={[styles.actionButtonText, { color: 'white' }]}>
                  {i18n.t('reimbursement.fullAmount')}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  };

  // 渲染报销模态框 - 优化UI设计
  const renderReimbursementModal = () => (
    <Modal
      visible={showReimbursementModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowReimbursementModal(false)}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowReimbursementModal(false)}
      >
        <View style={[styles.reimbursementModal, { backgroundColor: theme.surface }]} onStartShouldSetResponder={() => true}>
          <View style={styles.modalHeader}>
            <Text style={[styles.reimbursementModalTitle, { color: theme.text }]}>
              {i18n.t('reimbursement.customAmount')}
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowReimbursementModal(false)}
            >
              <Text style={[styles.closeButtonText, { color: theme.textSecondary }]}>✕</Text>
            </TouchableOpacity>
          </View>

          {selectedTransaction && (
            <View style={[styles.reimbursementInfo, { backgroundColor: theme.background }]}>
              <Text style={[styles.reimbursementInfoLabel, { color: theme.textSecondary }]}>
                {i18n.t('common.originalAmount')}
              </Text>
              <Text style={[styles.reimbursementInfoAmount, { color: theme.expense }]}>
                {currency}{Math.abs(selectedTransaction.amount).toFixed(2)}
              </Text>
            </View>
          )}

          <View style={styles.inputContainer}>
            <Text style={[styles.inputLabel, { color: theme.text }]}>
              {i18n.t('reimbursement.enterReimbursementAmount')}
            </Text>
            <TextInput
              style={[styles.reimbursementAmountInput, {
                backgroundColor: theme.background,
                borderColor: theme.border,
                color: theme.text
              }]}
              placeholder="0.00"
              placeholderTextColor={theme.textTertiary}
              value={reimbursementAmount}
              onChangeText={setReimbursementAmount}
              keyboardType="numeric"
              autoFocus
            />
          </View>

          <View style={styles.reimbursementModalButtons}>
            <TouchableOpacity
              style={[styles.reimbursementModalButton, styles.reimbursementCancelButton, { backgroundColor: theme.background }]}
              onPress={() => setShowReimbursementModal(false)}
            >
              <Text style={[styles.reimbursementCancelButtonText, { color: theme.textSecondary }]}>
                {i18n.t('common.cancel')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.reimbursementModalButton, styles.reimbursementConfirmButton, { backgroundColor: theme.primary }]}
              onPress={handlePartialReimbursement}
            >
              <Text style={styles.reimbursementConfirmButtonText}>{i18n.t('common.confirm')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const currentTransactions = activeTab === 'pending' ? pendingTransactions : completedTransactions;

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Tab Selector */}
      <View style={[styles.tabContainer, { borderBottomColor: theme.border }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'pending' && { borderBottomColor: theme.primary }
          ]}
          onPress={() => setActiveTab('pending')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'pending' ? theme.primary : theme.textSecondary }
          ]}>
            {i18n.t('reimbursement.pendingReimbursement')} ({pendingTransactions.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'completed' && { borderBottomColor: theme.primary }
          ]}
          onPress={() => setActiveTab('completed')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'completed' ? theme.primary : theme.textSecondary }
          ]}>
            {i18n.t('reimbursement.reimbursed')} ({completedTransactions.length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {currentTransactions.length === 0 ? (
          <EmptyState
            icon="receipt-outline"
            title={i18n.t('reimbursement.noReimbursementItems')}
            description={i18n.t('reimbursement.addReimbursementItemsDescription')}
          />
        ) : (
          currentTransactions.map(renderTransactionItem)
        )}
      </ScrollView>

      {renderReimbursementModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    marginHorizontal: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    minHeight: 48,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 20,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 44,
    height: 44,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  iconText: {
    fontSize: 20,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionCategory: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  transactionNote: {
    fontSize: 14,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  actionButtons: {
    flexDirection: 'column',
    gap: 8,
    minWidth: 100,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  customAmountButton: {
    borderWidth: 1,
  },
  fullAmountButton: {
    borderWidth: 0,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  reimbursedAmountContainer: {
    alignItems: 'flex-end',
    marginTop: 4,
  },
  reimbursedLabel: {
    fontSize: 10,
    fontWeight: '500',
    marginTop: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  reimbursementModal: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 0,
    width: '90%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
  },
  reimbursementModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  reimbursementInfo: {
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reimbursementInfoLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  reimbursementInfoAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  inputContainer: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  reimbursementAmountInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  reimbursementModalButtons: {
    flexDirection: 'row',
    gap: 12,
    padding: 20,
    paddingTop: 0,
  },
  reimbursementModalButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  reimbursementCancelButton: {
    borderWidth: 1,
    borderColor: '#ddd',
  },
  reimbursementConfirmButton: {
    shadowColor: '#dc4446',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  reimbursementCancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  reimbursementConfirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  reimbursedAmount: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
});

export default ReimbursementScreen;
